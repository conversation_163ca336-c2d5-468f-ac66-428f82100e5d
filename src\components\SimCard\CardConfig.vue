<template>
  <el-row class="cardConfigBox">
    <el-row class="left">
      <el-row
        class="item"
        @click="
          store.commit('SET_ScenarioShow', !store.state.app.ifScenarioShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifScenarioShow" /><Hide v-else
        /></el-icon>
        想定信息
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_LogBoxShow', !store.state.app.ifLogBoxShow)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifLogBoxShow" /><Hide v-else
        /></el-icon>
        日志
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EntityListBoxShow', !store.state.app.entityListShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.entityListShow" /><Hide v-else
        /></el-icon>
        实体列表
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_EntityInfoShow', !store.state.app.EntityInfo)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.EntityInfo" /><Hide v-else
        /></el-icon>
        实体信息
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EffectBoxShow', !store.state.app.effectBoxShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.effectBoxShow" /><Hide v-else
        /></el-icon>
        实体配置
      </el-row>
    </el-row>
    <el-row class="right">
      <el-row class="item">
        <div>比例：</div>
        <div class="scaleBar">
          <div class="scale-text">{{ scaleText }}</div>
          <div class="scale-graphic">
            <div class="tick"></div>
            <div class="line" :style="{ width: `${scaleWidth}px` }"></div>
            <div class="tick"></div>
          </div>
        </div>
      </el-row>
      <el-row class="item">
        <div>经度：</div>
        <div class="value">{{ longitude }}</div>
      </el-row>
      <el-row class="item">
        <div>维度：</div>
        <div class="value">{{ latitude }}</div>
      </el-row>
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { ref, onMounted, onUnmounted } from 'vue'

const store = useStore()

// 经纬度数据
const longitude = ref('-')
const latitude = ref('-')
const scaleText = ref('1000 km')
const scaleWidth = ref(95) // px

// 鼠标移动事件处理器
let mouseHandler: any = null
let scaleBarRetryCount = 0
function updateScaleBar() {
  if (!window.viewer) return
  const scene = window.viewer.scene
  const canvas = scene.canvas
  const y = canvas.height - 10
  const x1 = canvas.width / 2 - 47.5 // 95px/2
  const x2 = canvas.width / 2 + 47.5
  const p1 = scene.camera.pickEllipsoid(new Cesium.Cartesian2(x1, y))
  const p2 = scene.camera.pickEllipsoid(new Cesium.Cartesian2(x2, y))
  if (!p1 || !p2) return
  // 计算距离（米），兼容Cesium 1.5
  const c1 = new Cesium.Cartographic()
  const c2 = new Cesium.Cartographic()
  Cesium.Ellipsoid.WGS84.cartesianToCartographic(p1, c1)
  Cesium.Ellipsoid.WGS84.cartesianToCartographic(p2, c2)
  const R = Cesium.Ellipsoid.WGS84.maximumRadius
  const lat1 = c1.latitude
  const lon1 = c1.longitude
  const lat2 = c2.latitude
  const lon2 = c2.longitude
  // Haversine公式
  const dLat = lat2 - lat1
  const dLon = lon2 - lon1
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const meters = R * c
  // 只取指定的标准刻度
  const scaleList = [1000000, 500000, 200000, 100000, 50000, 20000, 10000, 1000, 500, 200, 100, 50, 20, 5, 2]
  // 取当前区间的最大best
  let best = scaleList.find(v => meters >= v) || scaleList[scaleList.length - 1]
  // 计算本区间的下一个best
  let nextBestIdx = scaleList.indexOf(best) - 1
  let nextBest = nextBestIdx >= 0 ? scaleList[nextBestIdx] : best
  // 区间内线性插值
  let t = (meters - best) / (nextBest - best)
  let width = Math.round(95 * (1 - t))
  width = Math.max(20, Math.min(95, width))
  // 文本
  let display = ''
  if (best >= 1000) {
    display = (best / 1000) + ' km'
  } else {
    display = best + ' m'
  }
  // 估算文字宽度（每字符8px，单位略小）
  const textLen = display.length * 8
  const minLineWidth = Math.max(textLen + 16, 20)
  scaleText.value = display
  scaleWidth.value = Math.max(minLineWidth, width)
}

onMounted(() => {
  // 等待viewer初始化
  const initInterval = setInterval(() => {
    if (window.viewer && window.GV) {
      clearInterval(initInterval)
      // 初始化鼠标移动监听
      mouseHandler = new Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      )
      mouseHandler.setInputAction((event: any) => {
        const position = event
        // 根据屏幕坐标获取坐标位置
        const point = window.GV.GeoPoint.fromScreen(
          position.endPosition.x,
          position.endPosition.y,
          window.viewer
        )
        if (point) {
          longitude.value = point.lon.toFixed(6)
          latitude.value = point.lat.toFixed(6)
        } else {
          longitude.value = '-'
          latitude.value = '-'
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
      // 监听相机变化
      window.viewer.camera.changed.addEventListener(updateScaleBar)
      // 初始化比例尺 相机不动 scene.camera.pickEllipsoid获取不到值
      updateScaleBar()
    }
  }, 100)
})

onUnmounted(() => {
  // 清理事件监听器
  if (mouseHandler) {
    mouseHandler.destroy()
    mouseHandler = null
  }
  if (window.viewer) {
    window.viewer.camera.moveEnd.removeEventListener(updateScaleBar)
  }
})
</script>

<style lang="less" scoped>
.cardConfigBox {
  position: fixed;
  bottom: 1px;
  z-index: 1;
  width: 100%;
  height: 32px;
  font-size: 15px;
  background-color: #002f49;
  border-top: 1px solid var(--app-border-color);
  padding: 0 20px;
  justify-content: space-between;
  .left {
    height: 100%;
    .item {
      cursor: pointer;
      align-items: center;
      margin-right: 20px;
      .icon {
        margin-right: 5px;
      }
    }
    .item:hover {
      color: #339af0;
    }
  }
  .right {
    height: 100%;
    .item {
      margin-right: 10px;
      align-items: center;
      .value {
        width: 85px;
      }
      .scaleBar {
        font-size: 14px;
        text-align: center;
        margin-right: 15px;
        .scale-text {
          margin-bottom: 2px;
        }
        .scale-graphic {
          display: flex;
          align-items: flex-start;
          justify-content: center;
          position: relative;
          height: 10px;
          .line {
            height: 1px;
            background: white;
            /* width: 60px; 由js动态设置 */
            width: v-bind(scaleWidth) px;
          }
          .tick {
            margin-top: -9px;
            width: 1px;
            height: 10px;
            background: white;
          }
        }
      }
    }
  }
}
</style>
