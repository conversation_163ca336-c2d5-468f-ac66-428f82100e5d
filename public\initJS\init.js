(function () {
  let thirdJsArr = [
      'config.js',
      'jquery-2.1.4.min.js'
  ]
  let scriptArr = Array.from(document.getElementsByTagName('script'))
  let host
  scriptArr.map(item => {
      let src = item.getAttribute('src')
      if (item.src.match('init.js')) {
          host ='/initJS/'
      }
  })
  thirdLoad(thirdJsArr, "js")
  window.GVJ = {}
  function thirdLoad(arr, type) {
      if (type === "js") {
          for (let i = 0; i < arr.length; i++) {
              document.write(`<script src='${host}${arr[i]}'></script>`)
          }
      }
  }
  
})()

