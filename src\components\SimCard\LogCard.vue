<template>
  <div
    class="logBox"
    @mouseleave="dataMove = true"
    @mousemove="dataMove = false"
  >
    <el-row class="title">
      <img src="/image/log_icon.png" alt="" class="icon" width="16" />
      <span>日志</span>
    </el-row>
    <div class="searchBox">
      <el-input
        v-model="value"
        style="width: 100%"
        placeholder="Please input"
        size="small"
        :suffix-icon="Search"
      />
    </div>
    <div class="timeLineBox" v-if="list.length">
      <el-timeline direction="vertical" ref="logBox">
        <el-timeline-item
          v-for="item in list"
          :key="item.sendTime"
          :timestamp="item.sendTime"
        >
          <!-- :timestamp="dayjs(item.sendTime).format('YYYY-MM-DD HH:mm:ss')" -->
          {{ item.messages }}
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-row v-else class="emptyBox">
      <Empty />
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { Search } from '@element-plus/icons'
import Empty from '@/components/Empty.vue'
const value = ref('')
const logBox = ref()
let dataMove = ref(false) // 鼠标是否移入
const fullList = ref<Array<{ messages: string; sendTime: string }>>([]) // 原始日志
const list = ref<Array<{ messages: string; sendTime: string }>>([]) // 当前展示日志
const sendMessage = val => {
  const message = {
    messages: val.data.name + ' ' + val.data.param,
    sendTime: val.time + 's',
  }
  fullList.value.push(message)
  filterList() // 每次添加后自动过滤
  // 更新数据时, 如果鼠标不在日志区域, 则追踪至列表最下方
  if (!dataMove.value && logBox.value) {
    nextTick(() => {
      logBox.value.$el.scrollTop = logBox.value.$el.scrollHeight
    })
  }
}
// 搜索过滤
const filterList = () => {
  const keyword = value.value.trim().toLowerCase()
  if (!keyword) {
    list.value = [...fullList.value]
  } else {
    list.value = fullList.value.filter(item =>
      item.messages.toLowerCase().includes(keyword)
    )
  }
}

// 清除日志
const clearList = () => {
  fullList.value = []
  list.value = []
}

// 搜索框监听输入变化
watch(value, filterList)
defineExpose({ clearList, sendMessage })
</script>
<style lang="less" scoped>
.logBox {
  height: calc(100% - 300px - 8px);
  position: absolute;
  width: 100%;
  top: calc(300px + 8px);
  background-color: #002941;
  border: 1px solid var(--app-border-color);
  border-left: none;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  .title {
    height: 32px;
    align-items: center;
    background-image: linear-gradient(90deg, #1f77ad 0%, #002942 100%);
    padding-left: 9px;
    .icon {
      margin-right: 8px;
    }
  }
  .searchBox {
    padding: 10px 18px;
  }
  .timeLineBox {
    padding: 0 18px;
    height: calc(100% - 32px - 46px);
    :deep(.el-timeline) {
      height: 100%;
      overflow: auto;
    }
  }
  .emptyBox {
    padding-top: 50px;
    flex-direction: column;
    align-items: center;
  }
}
</style>
