<template>
  <div class="content">
    <geovis></geovis>
  </div>
</template>

<script lang="ts" setup>
import geovis from '@/views/geovis.vue'
import { defineProps, ref, defineEmits } from 'vue'
document.oncontextmenu = e => {
  e.preventDefault()
}
</script>
<style scoped>
</style>
<style lang="sass">
#app_main
  font-family: Avenir, Helvetica, Arial, sans-serif
  -webkit-font-smoothing: antialiased
  -moz-osx-font-smoothing: grayscale
  color: #2c3e50

  &,
  html,
  body,
  .content
    font-size: 14px !important
    width: 100% !important
    height: 100% !important
    padding: 0 !important
    margin: 0 !important
    color: white !important
    overflow: hidden !important

div,img,p,span
  user-select: none
</style>
